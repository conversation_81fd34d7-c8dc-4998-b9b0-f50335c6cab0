'use client';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import MobileSidebar from '@/components/sidebar/mobile-sidebar';
import { Button } from '@/components/ui/button';
import { CopyButton } from '@/components/ui/copy-button';
import { useGetMe } from '@/hooks/use-get-me';
import { useIsMobile, useLgScreenSize } from '@/hooks/use-media-query';
import { useToast } from '@/hooks/use-toast';
import { VerificationStatus } from '@/lib/api/users/types';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { cn, openSupportEmail, userMissingFields } from '@/lib/utils';

const ReferPage = () => {
  const { me, isPending } = useGetMe();
  const router = useRouter();
  const t = useTranslations();
  const isMobile = useIsMobile();
  const isLgScreenSize = useLgScreenSize();
  const { toast } = useToast();

  const isVerified = useMemo(() => {
    return me?.verificationStatus === VerificationStatus.Verified;
  }, [me]);

  const isDenied = useMemo(() => {
    return me?.verificationStatus === VerificationStatus.Denied;
  }, [me]);

  const isCompletedWelcomeSurvey = useMemo(() => {
    if (!me) return false;
    return userMissingFields(me).length === 0;
  }, [me]);

  const content = useMemo(() => {
    if (!isVerified && isCompletedWelcomeSurvey) {
      return {
        title: t('referral.get_give', { bonus: me?.specialty?.referralValue }),
        description: t('referral.give_friend', { val: me?.specialty?.referralValue }),
        primaryButton: {
          text: t('referral.share_referral_code'),
          disabled: true,
        },
        copyButton: {
          text: '',
          isVisible: false,
        },
        subscription: true,
      };
    }

    if (!isCompletedWelcomeSurvey) {
      return {
        title: t('referral.get_cash_for_referrals'),
        description: t('referral.referral_text'),
        primaryButton: {
          text: t('referral.complete_welcome_survey'),
          disabled: isDenied,
        },
        copyButton: {
          text: '',
          isVisible: false,
        },
      };
    }

    return {
      title: t('referral.know_a_fellow_HCP'),
      description: t('referral.share_the_app'),
      primaryButton: {
        text: t('referral.share_referral_code'),
        disabled: false,
      },
      copyButton: {
        text: me?.referralCode || '',
        isVisible: true,
      },
    };
  }, [isVerified, isCompletedWelcomeSurvey, t, me?.referralCode, me?.specialty?.referralValue, isDenied]);

  const onPrimaryButtonClick = () => {
    if (!isCompletedWelcomeSurvey) {
      return router.push('/surveys');
    }

    const shareReferralLink = me?.referralLink || '';
    navigator.clipboard.writeText(shareReferralLink);

    toast({
      description: <CopyShareReferralLink />,
      position: !isLgScreenSize ? 'top-right' : 'bottom-center',
      variant: 'custom',
      className: 'bg-[#161733] text-white rounded-2xl !items-center !justify-center border-none ',
      duration: SUCCESS_TOAST_DURATION,
    });

    const subject = '';
    const body = t('referral.message_referral', {
      referralValue: me?.specialty?.referralValue,
      code: me?.referralCode,
      referralLink: me?.referralLink,
    });
    const email = '';

    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;
  };

  if (isPending) return null;

  return (
    <div className="flex h-full w-full flex-col gap-2 p-4 pb-16 pt-0 sm:py-0 sm:pr-0 xl:pr-[200px]">
      <SmallLogo />
      <h1 className="text-2xl font-bold">{!isMobile ? t('tab.refer') : t('referral.invite_friends')}</h1>
      <div className="mb-4 mt-4 flex w-full flex-col-reverse sm:flex-row sm:gap-10 sm:pb-0 lg:gap-20">
        <div className="flex flex-1 flex-col items-center space-y-4 sm:max-w-56 sm:items-start md:max-w-72">
          <p className="text-center text-3xl font-bold sm:text-left sm:text-base">{content.title}</p>
          <p
            className={cn(
              'w-3/4 text-center text-[17px] sm:w-full sm:text-left sm:text-[15px]',
              me?.verificationStatus === VerificationStatus.Verified && 'w-full',
            )}
          >
            {content.description}
          </p>
          <Button
            onClick={onPrimaryButtonClick}
            disabled={content.primaryButton.disabled}
            className="w-full min-w-52 px-4"
          >
            <span className="block truncate">{content.primaryButton.text}</span>
          </Button>
          {content.copyButton.isVisible && <CopyButton value={content.copyButton.text} className="w-full min-w-52" />}
          {content.subscription && me?.verificationStatus === VerificationStatus.Unverified && (
            <p className="pb-2 text-left text-[13px]">
              <span className="font-bold sm:block">{t('referral.your_profile_needs')}</span>{' '}
              {t('referral.please_contact_support')}
            </p>
          )}
          {me?.verificationStatus === VerificationStatus.Denied && (
            <p className="pb-2 text-left text-[13px] text-muted-foreground">
              <span className="font-bold text-foreground sm:block">{t('referral.your_profile_needs')}</span>{' '}
              {t.rich('common.verification_denied_description', {
                support: chunks => (
                  <Link href="#" className="text-primary" onClick={openSupportEmail}>
                    {chunks}
                  </Link>
                ),
              })}
            </p>
          )}
          {isCompletedWelcomeSurvey && (
            <p className="pb-2 text-left text-xs text-muted-foreground md:text-left">
              {t('referral.share_the_app_desc')}
            </p>
          )}
        </div>
        <div className="flex w-full flex-1 items-center justify-center px-10 sm:px-0 lg:px-14 xl:px-4">
          <Image
            src="/images/landing/landing-banner.svg"
            className="h-[226px] w-[270px] object-cover lg:h-auto lg:w-full xl:h-[300px] xl:w-[350px]"
            alt="refer"
            width={350}
            height={300}
          />
        </div>
      </div>
      <MobileSidebar />
    </div>
  );
};

export default ReferPage;

const CopyShareReferralLink = () => {
  const t = useTranslations();

  return (
    <div className="flex w-full items-center justify-center bg-[#161733] p-4 text-white">
      <Image
        src="/images/modal-icons/check-circle.svg"
        alt="Success"
        width={24}
        height={24}
        className="h-auto w-auto"
      />
      <p className="pl-2 text-base font-medium">{t('referral.referral_link_copied')}</p>
    </div>
  );
};
