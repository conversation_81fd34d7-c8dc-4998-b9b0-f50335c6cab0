'use client';

import { ChevronLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import type { UseFormReturn } from 'react-hook-form';

import { HomeSideImage } from '@/components/landing-side-images/home-side-image';
import PhoneVerification from '@/components/phone-verification';
import { Form } from '@/components/ui/form';
import SuccessToast from '@/components/ui/success-toast';
import { useAuthState } from '@/hooks/use-auth-state';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useOtpVerification } from '@/hooks/use-otp-verification';
import { useToast } from '@/hooks/use-toast';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { createRegisterSchema, type RegisterFormValues } from '@/lib/validation-schemas';

import { Congratulations } from './congratulations';
import { useRegistration } from './hooks/use-registration';
import { useVerifyInfo } from './hooks/use-verify-info';
import { RegisterFormContent } from './register-form-content';

export default function RegisterPage() {
  const t = useTranslations();
  const router = useRouter();
  const { toast, dismiss } = useToast();
  const searchParams = useSearchParams();
  const emailFromQuery = searchParams.get('email');

  // Custom hooks for state management and logic
  const { state, actions } = useAuthState();
  const { mutateAsync: verifyInfo, isPending: isVerifyInfoPending } = useVerifyInfo();
  const { mutateAsync: registration, isSuccess: isRegistrationSuccess, data: registrationData } = useRegistration();

  // Form validation hook
  const { validateRegistrationInfo } = useFormValidation({
    onSetShowModalPhoneExist: actions.setShowModalPhoneExist,
    onSetEmailExist: actions.setEmailExist,
  });

  // Create validation schema
  const registerSchema = createRegisterSchema(t);

  const defaultValues: RegisterFormValues = {
    firstName: '',
    lastName: '',
    phone: '',
    email: emailFromQuery || '',
    referralCode: '',
    isEmailOptIn: false,
  };

  // OTP verification hook
  const { handleResendOTP, handleSendOTP } = useOtpVerification({
    confirmationResult: state.confirmationResult,
    recaptchaVerifier: state.recaptchaVerifier,
    phoneNumber: state.phoneNumber,
    payload: state.payload,
    onSetConfirmationResult: actions.setConfirmationResult,
    onSetVerifyingOTP: actions.setVerifyingOTP,
    onSetInvalidOtp: actions.setInvalidOtp,
    onSetCompleted: actions.setCompleted,
  });

  // Enhanced OTP verification with registration logic
  const handleRegistrationOTP = async (code: string) => {
    actions.setVerifyingOTP(true);
    actions.setInvalidOtp(false);

    try {
      if (!state.confirmationResult || !state.payload) {
        throw new Error('Missing verification data');
      }

      const result = await state.confirmationResult.confirm(code);
      const idToken = await result.user.getIdToken();

      if (!idToken) {
        throw new Error('No id token found');
      }

      await registration({
        ...state.payload,
        idToken,
        preferredLanguage: navigator.language.toLowerCase().startsWith('fr') ? 'fr-ca' : 'en-ca',
        notificationEnabled: true,
      });

      toast({
        position: 'center',
        description: <SuccessToast />,
        className: 'rounded-xl py-2',
        hasOverlay: true,
      });

      setTimeout(() => {
        dismiss();
        actions.setCompleted(true);
      }, SUCCESS_TOAST_DURATION);
    } catch (error) {
      console.error('Error verifying OTP:', error);
      actions.setInvalidOtp(true);
    } finally {
      actions.setVerifyingOTP(false);
    }
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const onSubmit = async (data: RegisterFormValues, form?: UseFormReturn<RegisterFormValues>) => {
    if (!form) return;

    actions.setPhoneNumber(null);

    try {
      const verifyInfoResponse = await verifyInfo({
        phone: data.phone,
        email: data.email,
        referralCode: data.referralCode,
      });

      // Validate registration info using the validation hook
      const isValid = validateRegistrationInfo(verifyInfoResponse, form, data.referralCode);
      if (!isValid) {
        return;
      }

      // Send OTP using the OTP hook
      const result = await handleSendOTP(data.phone, form);
      if (result) {
        actions.setPhoneNumber(data.phone);
        actions.setPayload(data);
      }
    } catch (error) {
      console.error('Error during registration:', error);
    }
  };

  if (isRegistrationSuccess && state.isCompleted) {
    return (
      <>
        <Congratulations refUserInfo={registrationData?.data?.refUserInfo} />
        <div id="recaptcha-container"></div>
      </>
    );
  }

  return (
    <>
      <div className="flex w-full flex-1 items-center justify-center">
        <div
          className={cn(
            'flex h-full flex-1 flex-col px-4 sm:h-auto md:max-w-[356px] md:px-0',
            state.phoneNumber && 'hidden',
          )}
        >
          <div className="mb-8 flex items-center">
            <button className="-ml-2 mr-2" onClick={() => router.replace('/home')}>
              <ChevronLeft className="size-8 text-primary" />
            </button>
            <h1 className="text-2xl font-bold">{t('authentication.register')}</h1>
          </div>

          <div id="recaptcha-container"></div>

          <Form mode="onChange" defaultValues={defaultValues} schema={registerSchema} onSubmit={onSubmit}>
            <RegisterFormContent
              isVerifyInfoPending={isVerifyInfoPending}
              showModalPhoneExist={state.showModalPhoneExist}
              setShowModalPhoneExist={actions.setShowModalPhoneExist}
              isEmailExist={state.isEmailExist}
              onLogin={handleLogin}
            />
          </Form>
        </div>
        <div
          className={cn(state.phoneNumber ? 'mx-auto flex h-full max-w-[356px] flex-1 flex-col sm:h-auto' : 'hidden')}
        >
          <PhoneVerification
            phoneNumber={state.phoneNumber || ''}
            onVerificationComplete={handleRegistrationOTP}
            onClickBack={actions.resetPhoneVerification}
            onResendOTP={handleResendOTP}
            isInvalidOtp={state.isInvalidOtp}
            isVerifying={state.isVerifyingOTP}
            key={state.phoneNumber}
            onChangeInvalidOtp={actions.setInvalidOtp}
          />
        </div>
      </div>
      <HomeSideImage className="hidden md:flex" />
    </>
  );
}
