import { z } from 'zod';

import { inputPhoneSchema } from '@/components/ui/form/input-phone';

const NAME_REGEX = {
  firstName: /^[a-zA-Z \-À-ÿ]+$/,
  lastName: /^[a-zA-Z À-ÿ]+$/,
};

const ERROR_MESSAGES = {
  firstName: {
    required: 'First Name is required',
    pattern: "First name should only contain text and these characters: ⌴'-",
  },
  lastName: {
    required: 'Last Name is required',
    pattern: "Last name should only contain text and these characters: ⌴'-",
  },
};

export const createRegisterSchema = (t: (key: string) => string) => {
  return z.object({
    firstName: z
      .string({
        required_error: ERROR_MESSAGES.firstName.required,
      })
      .min(1, ERROR_MESSAGES.firstName.required)
      .regex(NAME_REGEX.firstName, ERROR_MESSAGES.firstName.pattern),
    lastName: z
      .string({
        required_error: ERROR_MESSAGES.lastName.required,
      })
      .min(1, ERROR_MESSAGES.lastName.required)
      .regex(NAME_REGEX.lastName, ERROR_MESSAGES.lastName.pattern),
    phone: inputPhoneSchema(t),
    email: z
      .string()
      .email()
      .refine(email => !email.includes("'"), t('error_validate_field.is_not_valid')),
    referralCode: z
      .string()
      .optional()
      .transform(val => val?.toUpperCase()),
    isEmailOptIn: z.boolean(),
  });
};

export type RegisterFormValues = z.infer<ReturnType<typeof createRegisterSchema>>;

export const getDefaultValues = (emailFromQuery?: string | null): RegisterFormValues => ({
  firstName: '',
  lastName: '',
  phone: '',
  email: emailFromQuery || '',
  referralCode: '',
  isEmailOptIn: false,
});
