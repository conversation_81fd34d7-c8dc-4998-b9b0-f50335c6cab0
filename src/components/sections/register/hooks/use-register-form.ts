import type { ConfirmationResult } from 'firebase/auth';
import { RecaptchaVerifier } from 'firebase/auth';
import { useState } from 'react';

import { auth } from '@/lib/firebase';

interface RegistrationState {
  phoneNumber: string | null;
  recaptchaVerifier: RecaptchaVerifier | null;
  confirmationResult: ConfirmationResult | null;
  isVerifyingOTP: boolean;
  isInvalidOtp: boolean;
  isCompleted: boolean;
  showModalPhoneExist: boolean;
  isEmailExist: boolean;
}

export function useRegisterForm() {
  const [state, setState] = useState<RegistrationState>({
    phoneNumber: null,
    recaptchaVerifier: null,
    confirmationResult: null,
    isVerifyingOTP: false,
    isInvalidOtp: false,
    isCompleted: false,
    showModalPhoneExist: false,
    isEmailExist: false,
  });

  const updateState = (updates: Partial<RegistrationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const resetPhoneVerification = () => {
    updateState({
      phoneNumber: null,
      isInvalidOtp: false,
    });
  };

  const initializeRecaptcha = () => {
    const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
      size: 'invisible',
      callback: () => {
        // reCAPTCHA solved, allow signInWithPhoneNumber.
      },
    });
    updateState({ recaptchaVerifier: verifier });
    return verifier;
  };

  const cleanupRecaptcha = () => {
    if (state.recaptchaVerifier) {
      state.recaptchaVerifier.clear();
    }
  };

  return {
    ...state,
    updateState,
    resetPhoneVerification,
    initializeRecaptcha,
    cleanupRecaptcha,
  };
}
