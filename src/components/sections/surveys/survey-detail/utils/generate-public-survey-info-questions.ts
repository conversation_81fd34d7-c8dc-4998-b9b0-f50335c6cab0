import type { QuestionType } from '@/lib/api/surveys';
import { Locale } from '@/lib/api/surveys/types';

import { CustomQuestionType } from '../types';

/**
 * Generates custom user information questions for public surveys
 */
export const generatePublicSurveyInfoQuestions = (surveyId: number, t: (key: string) => string) => {
  const emailQuestion = {
    id: -4,
    surveyId: surveyId,
    order: -1,
    questionType: CustomQuestionType.Email as unknown as QuestionType,
    locale: Locale.EN,
    title: t('public_survey.email_question'),
    subtitle: t('public_survey.email_optional'),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return [emailQuestion];
};
