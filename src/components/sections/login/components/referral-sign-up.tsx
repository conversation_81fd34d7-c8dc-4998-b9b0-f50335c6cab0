import Image from 'next/image';
import React from 'react';

import { Button } from '@/components/ui/button';

interface ReferralSignUpProps {
  onWelcomeSurveyClick?: () => void;
}

const ReferralSignUp: React.FC<ReferralSignUpProps> = ({ onWelcomeSurveyClick }) => {
  return (
    <div className="flex min-h-screen flex-col bg-white lg:flex-row">
      {/* Left Content Section */}
      <div className="flex flex-1 flex-col items-center justify-center px-6 py-8 lg:px-12 lg:py-16">
        {/* Logo */}
        <div className="mb-8 lg:mb-12">
          <Image
            src="/images/referral-sign-up.svg"
            alt="Referral Sign Up Illustration"
            width={1000}
            height={1000}
            className="h-full w-full object-cover"
          />
        </div>

        {/* Main Content */}
        <div className="w-full max-w-md space-y-6 text-center lg:space-y-8">
          {/* Title */}
          <h1 className="text-2xl font-bold tracking-wide text-[#161733] lg:text-3xl">Referral Sign Up</h1>

          {/* Description */}
          <p className="px-2 text-base leading-relaxed text-[#161733] lg:text-lg">
            Thank you for joining Industrii!
            <br />
            Please complete the Welcome Survey to receive your referral credit.
          </p>

          {/* Welcome Survey Button */}
          <Button
            onClick={onWelcomeSurveyClick}
            className="h-12 w-full max-w-[265px] rounded-lg bg-[#6149C4] font-semibold text-white shadow-[0px_4px_30px_rgba(97,73,196,0.2)] transition-colors duration-200 hover:bg-[#5038A3]"
          >
            Welcome Survey
          </Button>
        </div>
      </div>

      {/* Right Illustration Section - Hidden on mobile, shown on desktop */}
      <div className="relative hidden flex-1 items-center justify-center overflow-hidden bg-gradient-to-br from-[#f8f9ff] to-[#f0f2ff] lg:flex">
        <Image
          src="/images/referral-sign-up.svg"
          alt="Referral Sign Up Illustration"
          width={1000}
          height={1000}
          className="h-full w-full object-cover"
        />
      </div>
    </div>
  );
};

export default ReferralSignUp;
