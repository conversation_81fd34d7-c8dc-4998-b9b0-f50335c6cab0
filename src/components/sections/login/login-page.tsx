'use client';

import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import type { UseFormReturn } from 'react-hook-form';

import ButtonBack from '@/components/button-back/ButtonBack';
import { HomeSideImage } from '@/components/landing-side-images/home-side-image';
import PhoneVerification from '@/components/phone-verification';
import { Form } from '@/components/ui/form';
import SuccessToast from '@/components/ui/success-toast';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useLoginState } from '@/hooks/use-login-state';
import { useOtpVerification } from '@/hooks/use-otp-verification';
import useRecaptchaVerifier from '@/hooks/use-recaptcha-verifier';
import { useToast } from '@/hooks/use-toast';
import { auth } from '@/lib/api/auth';
import { handleFirebaseError } from '@/lib/auth-utils';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { createLoginSchema, type LoginFormValues } from '@/lib/validation-schemas';

import { useLogin } from './hooks/use-login';
import LoginFormContent from './login-form-content';

const LoginPage = () => {
  const t = useTranslations();
  const router = useRouter();
  const { toast, dismiss } = useToast();

  // State management
  const { state, actions } = useLoginState();
  const { mutate: onLogin } = useLogin();
  const recaptchaVerifier = useRecaptchaVerifier({ id: 'recaptcha-container' });

  // Form validation
  const { validateLoginInfo } = useFormValidation({
    onSetShowModalPhoneExist: () => {}, // Not used in login
    onSetEmailExist: () => {}, // Not used in login
  });

  // Create validation schema
  const loginSchema = createLoginSchema(t);

  const defaultValues: LoginFormValues = {
    phone: '',
  };

  // OTP verification hook
  const { handleSendOTP } = useOtpVerification({
    confirmationResult: state.confirmationResult,
    recaptchaVerifier,
    phoneNumber: state.phoneNumber,
    payload: null,
    onVerificationSuccess: (idToken: string) => {
      onLogin(idToken, {
        onSuccess: () => {
          toast({
            position: 'center',
            description: <SuccessToast />,
            className: 'rounded-xl py-2',
            hasOverlay: true,
          });
          setTimeout(() => {
            dismiss();
            router.replace('/surveys');
            router.refresh();
          }, SUCCESS_TOAST_DURATION);
        },
      });
    },
    onSetConfirmationResult: actions.setConfirmationResult,
    onSetVerifyingOTP: actions.setVerifyingOTP,
    onSetInvalidOtp: actions.setInvalidOtp,
  });

  const onSubmit = async (data: LoginFormValues, form?: UseFormReturn<LoginFormValues>) => {
    if (!form) return;

    actions.setVerifyingPhone(true);

    try {
      const result = await auth.phoneNumberVerification(data.phone);

      // Validate if phone exists
      const isValid = validateLoginInfo(result, actions.setOpenConfirmDialog);
      if (!isValid) {
        actions.setVerifyingPhone(false);
        return;
      }

      // Send OTP
      const otpResult = await handleSendOTP(data.phone, form);
      if (otpResult) {
        actions.setPhoneNumber(data.phone);
        actions.setPhoneVerified(true);
      }
    } catch (error) {
      console.error('Phone verification error:', error);
      handleFirebaseError(error, 'phone', form, t);
    } finally {
      actions.setVerifyingPhone(false);
    }
  };

  const onRegister = () => {
    router.push('/register');
  };

  const handleEditNumber = () => {
    actions.setOpenConfirmDialog(false);
    actions.setVerifyingPhone(false);
  };

  const onResendOTP = async () => {
    actions.setInvalidOtp(false);
    try {
      if (!state.phoneNumber) {
        throw new Error('No phone number found');
      }
      await handleSendOTP(state.phoneNumber);
    } catch (error) {
      console.error('Resend OTP error:', error);
    }
  };

  const handleVerifyOTP = async (otp: string) => {
    actions.setVerifyingOTP(true);
    actions.setInvalidOtp(false);

    try {
      if (!state.confirmationResult) {
        throw new Error('No confirmation result found');
      }

      const result = await state.confirmationResult.confirm(otp);
      const idToken = await result.user.getIdToken();

      if (!idToken) {
        throw new Error('No id token found');
      }

      onLogin(idToken, {
        onSuccess: () => {
          toast({
            position: 'center',
            description: <SuccessToast />,
            className: 'rounded-xl py-2',
            hasOverlay: true,
          });
          setTimeout(() => {
            dismiss();
            router.replace('/surveys');
            router.refresh();
          }, SUCCESS_TOAST_DURATION);
        },
      });
    } catch (error) {
      console.error('OTP verification error:', error);
      actions.setInvalidOtp(true);
    } finally {
      actions.setVerifyingOTP(false);
    }
  };

  return (
    <>
      <div className="flex w-full flex-1 items-start justify-center sm:items-center">
        <div id="recaptcha-container" />
        <div
          className={cn(
            'flex h-full flex-1 flex-col px-4 sm:h-auto md:max-w-[356px] md:px-0',
            state.isPhoneVerified && 'hidden',
          )}
        >
          <ButtonBack
            text={t('authentication.login')}
            className="justify-start"
            textStyles={'ml-2'}
            onClickBack={() => router.replace('/home')}
          />
          <p className="pt-6 sm:pt-14">{t('authentication.des_enter_mobile_number')}</p>
          <Form
            mode="onChange"
            schema={loginSchema}
            onSubmit={onSubmit}
            defaultValues={defaultValues}
            className="flex flex-1 flex-col justify-between pt-6"
          >
            <LoginFormContent
              openConfirmDialog={state.openConfirmDialog}
              onCloseConfirmDialog={() => actions.setOpenConfirmDialog(false)}
              onRegister={onRegister}
              handleEditNumber={handleEditNumber}
              isVerifyingPhone={state.isVerifyingPhone}
              isVerifyingOTP={state.isVerifyingOTP}
            />
          </Form>
        </div>
        <div
          className={cn(
            state.isPhoneVerified ? 'mx-auto flex h-full max-w-[356px] flex-1 flex-col sm:h-auto' : 'hidden',
          )}
        >
          <PhoneVerification
            phoneNumber={state.phoneNumber}
            onVerificationComplete={handleVerifyOTP}
            onClickBack={actions.resetPhoneVerification}
            key={state.phoneNumber}
            onResendOTP={onResendOTP}
            isInvalidOtp={state.isInvalidOtp}
            onChangeInvalidOtp={actions.setInvalidOtp}
            isVerifying={state.isVerifyingOTP}
          />
        </div>
      </div>
      <HomeSideImage className="hidden md:flex" />
    </>
  );
};

export default LoginPage;
