'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Label } from '@/components/ui/form/label';
import { FormTextareaAutoHeight } from '@/components/ui/form/textarea';
import { useIsMobile } from '@/hooks/use-media-query';

import { type CheckboxOption, CheckboxOptionQuestion } from '../../checkbox-option';
import type { QuestionProps } from '../../types';

type options = CheckboxOption & {
  isOther?: boolean;
};

type MultipleSelectQuestionProps = Partial<QuestionProps> & {
  options: options[];
  name: string;
  isSubmitting?: boolean;
  defaultValue: number[];
  defaultOtherValue?: string;
};

export const OtherMultipleSelectQuestion: React.FC<MultipleSelectQuestionProps> = ({
  options,
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  defaultOtherValue,
  subTitle,
  onChange,
}) => {
  const t = useTranslations();
  const [selectedOptions, setSelectedOptions] = useState<number[]>(() => defaultValue?.map(Number) || []);
  const [otherValue, setOtherValue] = useState<string | undefined>(() => defaultOtherValue);
  const [isOtherSelected, setIsOtherSelected] = useState<boolean>(
    selectedOptions.some(value => options.find(opt => opt.value === value)?.isOther === true),
  );
  const otherInputContainerRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const [otherInputHeight, setOtherInputHeight] = useState(0);
  useEffect(() => {
    setIsOtherSelected(selectedOptions.some(value => options.find(opt => opt.value === value)?.isOther === true));
  }, [selectedOptions, options]);

  useEffect(() => {
    setSelectedOptions(defaultValue?.map(Number) || []);
  }, [defaultValue]);

  useEffect(() => {
    setOtherValue(defaultOtherValue);
  }, [defaultOtherValue]);

  useEffect(() => {
    if ((isOtherSelected || otherInputHeight > 0) && otherInputContainerRef.current) {
      setTimeout(() => {
        otherInputContainerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }, 100);
    }
  }, [isOtherSelected, otherInputHeight]);

  const schema = z.object({
    [name]: z.array(z.number()).min(1, 'Please select at least one option'),
    [`${name}-otherValue`]: z
      .string()
      .optional()
      .refine(value => {
        if (isOtherSelected) {
          return !!value?.trim();
        }
        return true;
      }, 'Please specify other option'),
  });

  const defaultValues = useMemo(
    () => ({
      [name]: defaultValue ?? [],
      [`${name}-otherValue`]: defaultOtherValue,
    }),
    [name, defaultValue, defaultOtherValue],
  );

  const onSubmit = async (values: z.infer<typeof schema>) => {
    onNext?.({
      questionOptionIds: selectedOptions.map(String),
      value: isOtherSelected ? values[`${name}-otherValue`] : undefined,
    });
  };

  const handleOptionChange = (value: any) => {
    const selectedIds = value as string[];
    const selectedNumbers = selectedIds.map(Number);
    setSelectedOptions(selectedNumbers);

    const isOther = selectedIds.some(id => options.find(opt => opt.id === id)?.isOther === true);
    setIsOtherSelected(isOther);

    onChange?.({
      questionOptionIds: selectedIds,
      value: otherValue,
    });
  };

  const handleOtherInputChange = (value: string) => {
    setOtherValue(value);
    onChange?.({
      questionOptionIds: selectedOptions.map(String),
      value: value,
    });
  };

  return (
    <Form
      mode="onChange"
      key={`${name}-${isOtherSelected}`}
      schema={schema}
      onSubmit={onSubmit}
      defaultValues={defaultValues}
      className="no-scrollbar max-h-full flex-1 overflow-y-scroll px-4 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="flex h-full flex-col justify-between sm:flex-row">
        <div className="relative flex-1 overflow-y-auto pt-1">
          <div className="flex max-h-full flex-col gap-3 p-0.5 sm:max-h-[600px]">
            <CheckboxOptionQuestion
              options={options}
              name={name}
              className="flex max-h-full flex-col"
              label={<Label className="break-words text-[15px] font-medium leading-5">{subTitle}</Label>}
              onCheckedChange={handleOptionChange}
            />
            {isOtherSelected && (
              <div ref={otherInputContainerRef} className="mt-2 pb-4">
                <Label className="text-[15px] font-medium leading-5">{t('surveys.please_specify_below')}</Label>
                <FormTextareaAutoHeight
                  key={`${name}-otherValue`}
                  name={`${name}-otherValue`}
                  placeholder={t('surveys.text_type_placeholder')}
                  className="mt-2 box-content min-h-[22px] w-auto resize-none overflow-y-auto rounded-lg py-4 text-base"
                  minRows={1}
                  maxRows={isMobile ? undefined : 5}
                  hideError
                  onChange={handleOtherInputChange}
                  onHeightChange={setOtherInputHeight}
                />
              </div>
            )}
          </div>
        </div>
        <div className="mt-4 flex justify-end sm:mt-0 sm:flex-1">
          <Button
            type="submit"
            className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
            disableOnInvalid
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
