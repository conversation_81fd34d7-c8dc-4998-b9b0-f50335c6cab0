'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import { z } from 'zod';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';

import { FormInterceptor } from '../hooks/useFormInterceptor';
import type { QuestionProps } from './types';

type EmailQuestionProps = Partial<QuestionProps> & {
  name: string;
  defaultValue?: string;
  compensation?: number;
};

export const EmailQuestion = ({
  onNext,
  name,
  isSubmitting,
  defaultValue,
  onChange,
  compensation,
}: EmailQuestionProps) => {
  const t = useTranslations();

  const defaultValues = useMemo(() => ({ [name]: defaultValue }), [name, defaultValue]);
  const schema = z.object({
    [name]: z
      .string()
      .optional()
      .refine(value => {
        if (!value || value.trim() === '') return true; // Allow empty/blank values
        return z.string().email().safeParse(value).success; // Validate email format if provided
      }, 'invalid email')
      .refine(value => !value?.includes("'"), 'invalid characters'),
  });

  const onSubmit = async (value: z.infer<typeof schema>) => {
    onNext?.(value[name]);
  };

  return (
    <div className="relative flex h-full w-full max-w-[100vw] flex-col px-0 md:flex-row lg:gap-8">
      <SmallLogo />

      {/* Thank you image display only on mobile */}
      <div className="relative block md:hidden">
        <Image
          alt="Thank you image"
          src={'/images/surveys/public-survey-thanks.svg'}
          width={450}
          height={300}
          className="h-[250px] w-full object-contain"
        />
      </div>

      <div className="flex h-full w-full flex-col items-center justify-center gap-6 sm:max-w-[300px]">
        {/* Title and description */}
        <div className="flex flex-col items-start space-y-2 px-4 sm:px-0">
          <h1 className="text-start text-2xl font-bold lg:w-full">
            {compensation
              ? t('public_survey.your_compensation_awaits', { compensation })
              : t('public_survey.zero_compensation_awaits')}
          </h1>
          <span className="text-start text-[17px] leading-[22px] lg:w-full">
            {compensation
              ? t('public_survey.your_compensation_awaits_desc', { compensation })
              : t('public_survey.zero_compensation_awaits_desc')}
          </span>
        </div>
        <Form
          mode="onChange"
          schema={schema}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
          className="h-full w-full"
        >
          <FormInterceptor name={name} defaultValue={defaultValue} />
          <div className="flex flex-col justify-between gap-4 px-4 pb-4 sm:px-0">
            <div className="mb-4 flex w-full flex-col">
              <p className="mb-2 break-words text-[15px] font-medium">{t('authentication.email')}</p>
              <FormInput
                hideError
                name={name}
                type="email"
                placeholder={t('authentication.email_place_holder')}
                onChange={e => onChange?.(e.target.value)}
                className="h-14"
              />
            </div>
            <div className="flex w-full">
              <Button disableOnInvalid type="submit" className="w-full text-[15px]" loading={isSubmitting}>
                {t('public_survey.submit_survey')}
              </Button>
            </div>
          </div>
        </Form>
      </div>

      <div className="hidden md:block md:w-[467px]">
        <Image
          alt="Survey completion illustration"
          src="/images/surveys/wc-question-end.svg"
          width={650}
          height={500}
          className="h-auto w-full"
          priority
        />
      </div>
    </div>
  );
};
