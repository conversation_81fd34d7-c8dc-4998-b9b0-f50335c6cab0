import Image from 'next/image';

import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

interface Props {
  step: number;
  totalSteps: number;
  question: string;
}

const WelcomeStepBanner = ({ step, totalSteps, question }: Props) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className="h-2/5">
        <div className="relative flex h-full items-center justify-start overflow-hidden bg-primary sm:hidden [&>img]:!-top-6 [&>img]:!bottom-4">
          <Image alt="mask" src={'/images/surveys/wc-mask-mobile.svg'} fill className="h-full w-full object-cover" />
          <div className="z-[2] flex h-full w-full flex-col space-y-2 px-4 py-14 pt-16 text-white">
            <span className="text-[22px] font-bold text-white">
              {step} / <span className="text-[15px] text-white opacity-60">{totalSteps}</span>
            </span>
            <h1
              className={cn(
                'max-w-[350px] overflow-hidden overflow-y-auto break-words text-start font-bold',
                question.length > 110 ? 'text-base' : 'text-2xl',
              )}
            >
              {question}
            </h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="hidden min-h-36 w-full sm:flex">
      <div className="relative flex min-h-36 w-full items-center justify-start overflow-hidden rounded-2xl bg-primary">
        <Image
          className="absolute right-0 top-0 h-auto min-h-full w-auto"
          alt="bg-image"
          width={708}
          height={150}
          src={'/images/surveys/wc-question-bg.png'}
        />
        <div className="z-10 flex max-w-full flex-col space-y-2 px-5 text-white">
          <span className="text-[22px] font-bold text-white">
            {step} / <span className="text-[15px] text-white opacity-60">{totalSteps}</span>
          </span>
          <h1 className={cn('break-words font-bold', question.length > 110 ? 'text-base' : 'text-2xl')}>{question}</h1>
        </div>
      </div>
    </div>
  );
};

export default WelcomeStepBanner;
