import { FirebaseError } from 'firebase/app';

/**
 * Utility functions for authentication processes
 */

/**
 * Determines the preferred language based on browser language
 */
export const getPreferredLanguage = (): string => {
  const browserLang = navigator.language.toLowerCase();
  return browserLang.startsWith('fr') ? 'fr-ca' : 'en-ca';
};

/**
 * Maps Firebase error codes to user-friendly error messages
 */
export const getFirebaseErrorMessage = (error: FirebaseError, t: (key: string) => string): string => {
  const errorMessages: Record<string, string> = {
    'auth/too-many-requests': t('authentication.too_many_attempts'),
    'auth/invalid-phone-number': t('authentication.invalid_phone_number'),
    'auth/invalid-verification-code': t('error_validate_field.invalid_verification_code'),
  };

  return errorMessages[error.code] || t('authentication.send_verification_code_failed');
};

/**
 * Validates email format and checks for invalid characters
 */
export const validateEmail = (email: string, t: (key: string) => string): boolean | string => {
  if (email.includes("'")) {
    return t('error_validate_field.is_not_valid');
  }
  return true;
};

/**
 * Creates a standardized error object for form validation
 */
export const createFormError = (message: string) => ({
  type: 'manual' as const,
  message,
});

/**
 * Handles common Firebase authentication errors and sets form errors
 */
export const handleFirebaseError = (error: unknown, fieldName: string, form: any, t: (key: string) => string): void => {
  console.error(`Error in ${fieldName}:`, error);

  if (error instanceof FirebaseError) {
    const errorMessage = getFirebaseErrorMessage(error, t);
    form?.setError(fieldName, createFormError(errorMessage));
  } else {
    form?.setError(fieldName, createFormError(t('authentication.send_verification_code_failed')));
  }
};

/**
 * Validates required verification data
 */
export const validateVerificationData = (
  confirmationResult: any,
  payload: any,
): { isValid: boolean; error?: string } => {
  if (!confirmationResult) {
    return { isValid: false, error: 'No confirmation result found' };
  }

  if (!payload) {
    return { isValid: false, error: 'No payload found' };
  }

  return { isValid: true };
};

/**
 * Extracts ID token from Firebase auth result
 */
export const extractIdToken = async (result: any): Promise<string> => {
  const idToken = await result.user.getIdToken();

  if (!idToken) {
    throw new Error('No id token found');
  }

  return idToken;
};

/**
 * Creates registration payload with default values
 */
export const createRegistrationPayload = (payload: any, idToken: string) => ({
  ...payload,
  idToken,
  preferredLanguage: getPreferredLanguage(),
  notificationEnabled: true,
});
