import type { TFunction } from 'next-intl';
import { z } from 'zod';

import { inputPhoneSchema } from '@/components/ui/form/input-phone';
import { validateEmail } from '@/lib/auth-utils';

/**
 * Validation schemas for authentication forms
 */

export const createRegisterSchema = (t: TFunction) =>
  z.object({
    firstName: z
      .string({
        required_error: 'First Name is required',
      })
      .min(1, 'First Name is required')
      .regex(/^[a-zA-Z \-À-ÿ]+$/, "First name should only contain text and these characters: ⌴'-"),
    lastName: z
      .string({
        required_error: 'Last Name is required',
      })
      .min(1, 'Last Name is required')
      .regex(/^[a-zA-Z À-ÿ]+$/, "Last name should only contain text and these characters: ⌴'-"),
    phone: inputPhoneSchema(t),
    email: z
      .string()
      .email()
      .refine((email) => validateEmail(email, t), t('error_validate_field.is_not_valid')),
    referralCode: z
      .string()
      .optional()
      .transform((val) => val?.toUpperCase()),
    isEmailOptIn: z.boolean(),
  });

export const createLoginSchema = (t: TFunction) =>
  z.object({
    phone: inputPhoneSchema(t),
  });

export type RegisterFormValues = z.infer<ReturnType<typeof createRegisterSchema>>;
export type LoginFormValues = z.infer<ReturnType<typeof createLoginSchema>>;
