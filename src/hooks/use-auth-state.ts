import type { ConfirmationResult } from 'firebase/auth';
import { RecaptchaVerifier } from 'firebase/auth';
import { useEffect, useReducer } from 'react';

import { auth } from '@/lib/firebase';

/**
 * Authentication state management hook
 * Consolidates related authentication state into a single reducer
 */

interface AuthState {
  phoneNumber: string | null;
  recaptchaVerifier: RecaptchaVerifier | null;
  confirmationResult: ConfirmationResult | null;
  isVerifyingOTP: boolean;
  isInvalidOtp: boolean;
  isCompleted: boolean;
  showModalPhoneExist: boolean;
  isEmailExist: boolean;
  payload: any;
}

type AuthAction =
  | { type: 'SET_PHONE_NUMBER'; payload: string | null }
  | { type: 'SET_RECAPTCHA_VERIFIER'; payload: RecaptchaVerifier | null }
  | { type: 'SET_CONFIRMATION_RESULT'; payload: ConfirmationResult | null }
  | { type: 'SET_VERIFYING_OTP'; payload: boolean }
  | { type: 'SET_INVALID_OTP'; payload: boolean }
  | { type: 'SET_COMPLETED'; payload: boolean }
  | { type: 'SET_SHOW_MODAL_PHONE_EXIST'; payload: boolean }
  | { type: 'SET_EMAIL_EXIST'; payload: boolean }
  | { type: 'SET_PAYLOAD'; payload: any }
  | { type: 'RESET_PHONE_VERIFICATION' }
  | { type: 'RESET_OTP_STATE' };

const initialState: AuthState = {
  phoneNumber: null,
  recaptchaVerifier: null,
  confirmationResult: null,
  isVerifyingOTP: false,
  isInvalidOtp: false,
  isCompleted: false,
  showModalPhoneExist: false,
  isEmailExist: false,
  payload: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_PHONE_NUMBER':
      return { ...state, phoneNumber: action.payload };
    case 'SET_RECAPTCHA_VERIFIER':
      return { ...state, recaptchaVerifier: action.payload };
    case 'SET_CONFIRMATION_RESULT':
      return { ...state, confirmationResult: action.payload };
    case 'SET_VERIFYING_OTP':
      return { ...state, isVerifyingOTP: action.payload };
    case 'SET_INVALID_OTP':
      return { ...state, isInvalidOtp: action.payload };
    case 'SET_COMPLETED':
      return { ...state, isCompleted: action.payload };
    case 'SET_SHOW_MODAL_PHONE_EXIST':
      return { ...state, showModalPhoneExist: action.payload };
    case 'SET_EMAIL_EXIST':
      return { ...state, isEmailExist: action.payload };
    case 'SET_PAYLOAD':
      return { ...state, payload: action.payload };
    case 'RESET_PHONE_VERIFICATION':
      return {
        ...state,
        phoneNumber: null,
        isInvalidOtp: false,
      };
    case 'RESET_OTP_STATE':
      return {
        ...state,
        isVerifyingOTP: false,
        isInvalidOtp: false,
      };
    default:
      return state;
  }
};

export const useAuthState = () => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize reCAPTCHA verifier
  useEffect(() => {
    const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
      size: 'invisible',
      callback: () => {
        // reCAPTCHA solved, allow signInWithPhoneNumber.
      },
    });
    dispatch({ type: 'SET_RECAPTCHA_VERIFIER', payload: verifier });

    return () => {
      verifier.clear();
    };
  }, []);

  const actions = {
    setPhoneNumber: (phoneNumber: string | null) => dispatch({ type: 'SET_PHONE_NUMBER', payload: phoneNumber }),
    setRecaptchaVerifier: (verifier: RecaptchaVerifier | null) =>
      dispatch({ type: 'SET_RECAPTCHA_VERIFIER', payload: verifier }),
    setConfirmationResult: (result: ConfirmationResult | null) =>
      dispatch({ type: 'SET_CONFIRMATION_RESULT', payload: result }),
    setVerifyingOTP: (isVerifying: boolean) => dispatch({ type: 'SET_VERIFYING_OTP', payload: isVerifying }),
    setInvalidOtp: (isInvalid: boolean) => dispatch({ type: 'SET_INVALID_OTP', payload: isInvalid }),
    setCompleted: (isCompleted: boolean) => dispatch({ type: 'SET_COMPLETED', payload: isCompleted }),
    setShowModalPhoneExist: (show: boolean) => dispatch({ type: 'SET_SHOW_MODAL_PHONE_EXIST', payload: show }),
    setEmailExist: (exists: boolean) => dispatch({ type: 'SET_EMAIL_EXIST', payload: exists }),
    setPayload: (payload: any) => dispatch({ type: 'SET_PAYLOAD', payload }),
    resetPhoneVerification: () => dispatch({ type: 'RESET_PHONE_VERIFICATION' }),
    resetOtpState: () => dispatch({ type: 'RESET_OTP_STATE' }),
  };

  return { state, actions };
};
