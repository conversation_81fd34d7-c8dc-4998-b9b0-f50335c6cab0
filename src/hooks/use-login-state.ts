import type { ConfirmationResult } from 'firebase/auth';
import { useReducer } from 'react';

/**
 * Login-specific state management hook
 * Simplified version of auth state for login flow
 */

interface LoginState {
  openConfirmDialog: boolean;
  phoneNumber: string;
  confirmationResult: ConfirmationResult | null;
  isPhoneVerified: boolean;
  isVerifyingPhone: boolean;
  isInvalidOtp: boolean;
  isVerifyingOTP: boolean;
}

type LoginAction =
  | { type: 'SET_OPEN_CONFIRM_DIALOG'; payload: boolean }
  | { type: 'SET_PHONE_NUMBER'; payload: string }
  | { type: 'SET_CONFIRMATION_RESULT'; payload: ConfirmationResult | null }
  | { type: 'SET_PHONE_VERIFIED'; payload: boolean }
  | { type: 'SET_VERIFYING_PHONE'; payload: boolean }
  | { type: 'SET_INVALID_OTP'; payload: boolean }
  | { type: 'SET_VERIFYING_OTP'; payload: boolean }
  | { type: 'RESET_PHONE_VERIFICATION' };

const initialState: LoginState = {
  openConfirmDialog: false,
  phoneNumber: '',
  confirmationResult: null,
  isPhoneVerified: false,
  isVerifyingPhone: false,
  isInvalidOtp: false,
  isVerifyingOTP: false,
};

const loginReducer = (state: LoginState, action: LoginAction): LoginState => {
  switch (action.type) {
    case 'SET_OPEN_CONFIRM_DIALOG':
      return { ...state, openConfirmDialog: action.payload };
    case 'SET_PHONE_NUMBER':
      return { ...state, phoneNumber: action.payload };
    case 'SET_CONFIRMATION_RESULT':
      return { ...state, confirmationResult: action.payload };
    case 'SET_PHONE_VERIFIED':
      return { ...state, isPhoneVerified: action.payload };
    case 'SET_VERIFYING_PHONE':
      return { ...state, isVerifyingPhone: action.payload };
    case 'SET_INVALID_OTP':
      return { ...state, isInvalidOtp: action.payload };
    case 'SET_VERIFYING_OTP':
      return { ...state, isVerifyingOTP: action.payload };
    case 'RESET_PHONE_VERIFICATION':
      return {
        ...state,
        isPhoneVerified: false,
        isInvalidOtp: false,
      };
    default:
      return state;
  }
};

export const useLoginState = () => {
  const [state, dispatch] = useReducer(loginReducer, initialState);

  const actions = {
    setOpenConfirmDialog: (open: boolean) => dispatch({ type: 'SET_OPEN_CONFIRM_DIALOG', payload: open }),
    setPhoneNumber: (phoneNumber: string) => dispatch({ type: 'SET_PHONE_NUMBER', payload: phoneNumber }),
    setConfirmationResult: (result: ConfirmationResult | null) =>
      dispatch({ type: 'SET_CONFIRMATION_RESULT', payload: result }),
    setPhoneVerified: (verified: boolean) => dispatch({ type: 'SET_PHONE_VERIFIED', payload: verified }),
    setVerifyingPhone: (verifying: boolean) => dispatch({ type: 'SET_VERIFYING_PHONE', payload: verifying }),
    setInvalidOtp: (invalid: boolean) => dispatch({ type: 'SET_INVALID_OTP', payload: invalid }),
    setVerifyingOTP: (verifying: boolean) => dispatch({ type: 'SET_VERIFYING_OTP', payload: verifying }),
    resetPhoneVerification: () => dispatch({ type: 'RESET_PHONE_VERIFICATION' }),
  };

  return { state, actions };
};
