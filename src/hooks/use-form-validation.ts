import { useTranslations } from 'next-intl';
import { useCallback } from 'react';
import type { UseFormReturn } from 'react-hook-form';

import { createFormError } from '@/lib/auth-utils';

/**
 * Custom hook for form validation logic
 * Handles common validation patterns for authentication forms
 */

interface VerifyInfoResponse {
  data?: {
    isPhoneExist?: boolean;
    isEmailExist?: boolean;
    isRefCodeValid?: boolean;
  };
}

interface UseFormValidationProps {
  onSetShowModalPhoneExist: (show: boolean) => void;
  onSetEmailExist: (exists: boolean) => void;
}

export const useFormValidation = ({ onSetShowModalPhoneExist, onSetEmailExist }: UseFormValidationProps) => {
  const t = useTranslations();

  const validateRegistrationInfo = useCallback(
    (verifyInfoResponse: VerifyInfoResponse, form: UseFormReturn<any>, referralCode?: string): boolean => {
      // Check if phone exists
      if (verifyInfoResponse.data?.isPhoneExist) {
        onSetShowModalPhoneExist(true);
        return false;
      }

      // Check if email exists
      if (verifyInfoResponse.data?.isEmailExist) {
        form.setError('email', createFormError(t('profile.email_already')));
        onSetEmailExist(true);
        return false;
      }

      // Check referral code validity
      if (!verifyInfoResponse.data?.isRefCodeValid && referralCode) {
        form.setError('referralCode', createFormError(t('error.Invalid referral code')));
        return false;
      }

      return true;
    },
    [onSetShowModalPhoneExist, onSetEmailExist, t],
  );

  const validateLoginInfo = useCallback(
    (
      verifyInfoResponse: { data: { isPhoneExist: boolean } },
      onSetOpenConfirmDialog: (open: boolean) => void,
    ): boolean => {
      if (!verifyInfoResponse.data.isPhoneExist) {
        onSetOpenConfirmDialog(true);
        return false;
      }
      return true;
    },
    [],
  );

  return {
    validateRegistrationInfo,
    validateLoginInfo,
  };
};
