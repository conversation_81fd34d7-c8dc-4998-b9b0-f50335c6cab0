import { FirebaseError } from 'firebase/app';
import type { ConfirmationResult, RecaptchaVerifier } from 'firebase/auth';
import { useTranslations } from 'next-intl';
import { useCallback } from 'react';

import { useToast } from '@/hooks/use-toast';
import {
  createRegistrationPayload,
  extractIdToken,
  handleFirebaseError,
  validateVerificationData,
} from '@/lib/auth-utils';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { sendOTP } from '@/lib/firebase';

import SuccessToast from '@/components/ui/success-toast';

/**
 * Custom hook for OTP verification logic
 * Handles OTP sending, verification, and resending
 */

interface UseOtpVerificationProps {
  confirmationResult: ConfirmationResult | null;
  recaptchaVerifier: RecaptchaVerifier | null;
  phoneNumber: string | null;
  payload: any;
  onVerificationSuccess?: (data?: any) => void;
  onSetConfirmationResult: (result: ConfirmationResult | null) => void;
  onSetVerifyingOTP: (isVerifying: boolean) => void;
  onSetInvalidOtp: (isInvalid: boolean) => void;
  onSetCompleted?: (isCompleted: boolean) => void;
}

export const useOtpVerification = ({
  confirmationResult,
  recaptchaVerifier,
  phoneNumber,
  payload,
  onVerificationSuccess,
  onSetConfirmationResult,
  onSetVerifyingOTP,
  onSetInvalidOtp,
  onSetCompleted,
}: UseOtpVerificationProps) => {
  const t = useTranslations();
  const { toast, dismiss } = useToast();

  const handleVerifyOTP = useCallback(
    async (code: string) => {
      onSetVerifyingOTP(true);
      onSetInvalidOtp(false);

      try {
        const validation = validateVerificationData(confirmationResult, payload);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }

        const result = await confirmationResult!.confirm(code);
        const idToken = await extractIdToken(result);

        if (onVerificationSuccess) {
          // For login flow
          onVerificationSuccess(idToken);
        } else {
          // For registration flow
          const registrationPayload = createRegistrationPayload(payload, idToken);

          // This would be passed from the parent component
          // await registration(registrationPayload);

          toast({
            position: 'center',
            description: <SuccessToast />,
            className: 'rounded-xl py-2',
            hasOverlay: true,
          });

          setTimeout(() => {
            dismiss();
            onSetCompleted?.(true);
          }, SUCCESS_TOAST_DURATION);
        }
      } catch (error) {
        console.error('Error verifying OTP:', error);
        if (error instanceof FirebaseError) {
          onSetInvalidOtp(true);
        }
      } finally {
        onSetVerifyingOTP(false);
      }
    },
    [
      confirmationResult,
      payload,
      onVerificationSuccess,
      onSetVerifyingOTP,
      onSetInvalidOtp,
      onSetCompleted,
      toast,
      dismiss,
    ]
  );

  const handleResendOTP = useCallback(async () => {
    onSetInvalidOtp(false);

    try {
      if (!recaptchaVerifier || !phoneNumber) {
        throw new Error('Missing required verification data');
      }

      const result = await sendOTP(phoneNumber, recaptchaVerifier);
      onSetConfirmationResult(result);
    } catch (error) {
      console.error('Error resending OTP:', error);
      if (error instanceof FirebaseError) {
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? t('authentication.too_many_attempts')
            : t('authentication.send_verification_code_failed');

        throw new Error(errorMessage);
      }
    }
  }, [recaptchaVerifier, phoneNumber, onSetInvalidOtp, onSetConfirmationResult, t]);

  const handleSendOTP = useCallback(
    async (phone: string, form?: any) => {
      try {
        if (!recaptchaVerifier) {
          throw new Error('reCAPTCHA not initialized');
        }

        const result = await sendOTP(phone, recaptchaVerifier);
        onSetConfirmationResult(result);
        return result;
      } catch (error) {
        if (form) {
          handleFirebaseError(error, 'phone', form, t);
        }
        throw error;
      }
    },
    [recaptchaVerifier, onSetConfirmationResult, t]
  );

  return {
    handleVerifyOTP,
    handleResendOTP,
    handleSendOTP,
  };
};
