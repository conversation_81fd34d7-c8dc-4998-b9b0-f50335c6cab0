'use client';
import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { signOut } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { type PropsWithChildren, useState } from 'react';

import { type ToastErrorOptions, useErrorToast } from '@/hooks/use-errors';
import type { ApiError } from '@/lib/api/types';
import { ErrorCode } from '@/lib/errors';
import { auth } from '@/lib/firebase';
import { removeLocalStorage } from '@/lib/local-storage';

type CreateQueryClientPayload = {
  toastError: (options: ToastErrorOptions) => void;
  handleLogout: () => Promise<void>;
};

const createQueryClient = ({ toastError, handleLogout }: CreateQueryClientPayload) => {
  const handleError = async (error: unknown) => {
    const apiError = error as unknown as ApiError;

    // Handle unauthorized "User not found" error
    if (apiError.code === ErrorCode.Unauthorized && apiError.message === 'User not found') {
      await handleLogout();
    }

    // Skip toast for survey not found errors - let the component handle it
    if (apiError.code === ErrorCode.NotFound) {
      return;
    }

    toastError({
      error: apiError,
    });
  };

  return new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: 1,
      },
    },
    queryCache: new QueryCache({
      onError: error => {
        handleError(error);
      },
    }),
    mutationCache: new MutationCache({
      onError: error => {
        handleError(error);
      },
    }),
  });
};

export default function ReactQueryProvider({ children }: PropsWithChildren) {
  const router = useRouter();
  const toastError = useErrorToast();

  const handleLogout = async () => {
    try {
      await signOut(auth);
      removeLocalStorage('accessToken');
      removeLocalStorage('refreshToken');
      await fetch('/api/auth/logout', { method: 'GET' });
      // Note: We can't call queryClient.clear() here since we're inside the error handler
      // The page redirect will handle the cleanup
      router.replace('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const [queryClient] = useState(() => createQueryClient({ toastError, handleLogout }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
    </QueryClientProvider>
  );
}
