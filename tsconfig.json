{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": "src/", "paths": {"@/*": ["./src/*"], "@/components/*": ["components/*"], "@/sections/*": ["components/sections/*"], "@/assets/*": ["assets/*"], "@/lib/*": ["lib/*"], "@/hooks/*": ["hooks/*"], "@/config": ["config"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}